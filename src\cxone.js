var _cxOneInstance = null;

/**
 * 
 * @return
 */
function getCxOneInstance() {
	if (_cxOneInstance === null) {
		_cxOneInstance = cxOneFactory();
	}
	
	return _cxOneInstance;
}

/**
 * 
 * @return
 */
function cxOneFactory() {

	var state = {
		demoGraphics: null
	};
	
	/**
	 * 
	 * @param callNo
	 * @return
	  {
		  "success": true,
		  "data": {
		    "CallNo": 250681398,
		    "CxOneMode": 1,
		    "CallStatusValue": -100,
		    "CallForename": "<PERSON>",
		    "CallSurname": "CxOneDoeTwo",
		    "CallMF": "Male",
		    "CallAddress1": "10 Downing Street",
		    "CallAddress2": "",
		    "CallAddress3": "",
		    "CallTown": "London",
		    "CallPostCode": "SW1A 1AA",
		    "PatientAddress1": "10 Downing Street",
		    "PatientAddress2": "",
		    "PatientAddress3": "",
		    "PatientTown": "London",
		    "PatientPostCode": "SW1A 1AA",
		    "CallCName": "<PERSON> Doe",
		    "CallCRel": "Parent",
		    "CallDOB": "15/06/1985",
		    "CallEthnicity": "White British",
		    "CallLanguage": "English",
		    "ElectronicRecordConsent": "Yes",
		    "PreferredContact": "Phone",
		    "CallSymptoms": "some illness"
		  },
		  "message": ""
		}
	 */
	function getDemographics(callNo) {
		// /calld.nsf/(cxoneapi)?openagent&ACTION=GET_DEMOGRAPHICS&CALLNO=250681398
		var url = MyGlobalSession.Global_DB_Paths.HOST_PATH + "/" + MyGlobalSession.Global_DB_Paths.PATH_CALL + "/(cxoneapi)?openagent&ACTION=GET_DEMOGRAPHICS&CALLNO=" + callNo;
		return localCache.getUrlDataWithCache(url, false);
	}
	

    /*
     * 
     * @param payload {
     * 	ContactID: string;      Unique ID for the call session
     * 	CaseID?: string;        ID of the matched case, if available
     * 	UserEmail: string;      Agent’s email address - User Principal Name
     * 	Type: string;           One of NLP_MATCHED, NLP_UNMATCHED, NO_PDI, SMS_MATCHED 
     * 	PhoneNumber: string;    The phone number the patient is calling from
     * }
     * @return void
     */
    function processPayloadForNewCall(payload) {
        var serviceName = "CAS";
        var callServiceAlt = "";
        var cleoClientService = "";


        if (payload && payload.CaseID && payload.CaseID.length > 0) {
            // We don't care how it was matched..
            // We can launch the case directly and load whatever info was entered.
            // var launchOptions = {
            //     cxOneCallNo: payload.CaseID // this triggers loadNewCall() when new case launched.
            // }
            // CallControllerClient.loadNewCallDocument(serviceName, callServiceAlt, cleoClientService, launchOptions);
            openCall(payload.CaseID);   
            return;
        }

        // if no case number, we have 2 choices:
        // NO_PDI: the demographic data passed, so launch a new case, I guess that save user having to create a new
        // case, or would that be confusing????
        if (payload && payload.Type && payload.Type === "NO_PDI") {
            CallControllerClient.loadNewCallDocument(serviceName, callServiceAlt, cleoClientService);
            return;
        }

        // Send user to a grid and tey can see if they can find case...or would it be just as simple to launch 
        // a new case and get user to go through standard: Surname, first name, DOB, etc.?????
        // loadSomeView()...?
        window.openViewPanel('CXONE', 60000, '')
    }

	
	/**
	 * 
	 * @param callNo
	 * @return
	 */
	function loadNewCall(callNo) {
		return getDemographics(callNo).then(function(resp) {
			if (resp.success) {
				state.demoGraphics = resp.data;
			}
			return resp;
		})
	}
	
	/**
	 * 
	 * @return
	 */
	function mapDemographicsToCase(userLookupController) {
		
		if (!state.demoGraphics) {
			console.log("cxOneFactory.mapDemographicsToCase() no demographics to load");
			return;
		}
		
		var FIELD_SUFFIX = userLookupController.getFIELD_SUFFIX();	
	
		// <FIELDS_SPECIFIC_TO_SEARCH>
		userLookupController.setFieldAndUpdateStore("firstName", "SearchForename", state.demoGraphics.CallForename)
		userLookupController.setFieldAndUpdateStore("lastname", "SearchSurname", state.demoGraphics.CallSurname)
		userLookupController.setFieldAndUpdateStore("gender", "SearchMF", state.demoGraphics.CallMF)
		userLookupController.setFieldAndUpdateStore("dob", "SearchDOB", state.demoGraphics.CallDOB)
		userLookupController.setFieldAndUpdateStore("postCode", "SearchPostCode", state.demoGraphics.CallPostCode)
		// </FIELDS_SPECIFIC_TO_SEARCH>
		

		$("#CallAddress1" + FIELD_SUFFIX).val(state.demoGraphics.CallAddress1)
		$("#CallAddress2" + FIELD_SUFFIX).val(state.demoGraphics.CallAddress2)
		$("#CallAddress3" + FIELD_SUFFIX).val(state.demoGraphics.CallAddress3)
		$("#CallTown" + FIELD_SUFFIX).val(state.demoGraphics.CallTown)
		$("#CallPostCode" + FIELD_SUFFIX).val(state.demoGraphics.CallPostCode)
		
		$("#PatientAddress1" + FIELD_SUFFIX).val(state.demoGraphics.PatientAddress1)
		$("#PatientAddress2" + FIELD_SUFFIX).val(state.demoGraphics.PatientAddress2)
		$("#PatientAddress3" + FIELD_SUFFIX).val(state.demoGraphics.PatientAddress3)
		$("#PatientTown" + FIELD_SUFFIX).val(state.demoGraphics.PatientTown)
		$("#PatientPostCode" + FIELD_SUFFIX).val(state.demoGraphics.PatientPostCode)
		
		$("#CallSymptoms" + FIELD_SUFFIX).val(state.demoGraphics.CallSymptoms)
		
		$("#CallCName").val(state.demoGraphics.CallCName)
		
		// should we add a value that doesn't exist in CLEO list?
		$("#CallCRel").val(state.demoGraphics.CallCRel)
		
		// should we add a value that doesn't exist in CLEO list?
		$("#CallEthnicity").val(state.demoGraphics.CallEthnicity)
		
		
		$("#CallTelNo_R").val(state.demoGraphics.CallTelNo_R)
		
		
		// <Service>
		var service = "CAS"
		$("#CallService").val("CAS");
		$("#CallServiceType").val("OOH");
		if ( CallControllerClient.isOneOneOneService(service) ){
			$("#CallServiceType").val("111");
		}
		$("#CallServiceType").html(service);
		
		CallControllerClient.setFieldValue("CallService_span", service);
		// </Service>
		
		CallControllerClient.setFieldValue("CallClassification", "Advice");
		CallControllerClient.setFieldValue("CallClassification_label", "Advice");
		
		ApplicationControllerClient.initProtocolSection();
		CallControllerClient.displayRecentCallsLabel();
		CallControllerClient.loadClassificationChoices(CallControllerClient.getFieldValue("CallClassification"));
		
		// if 1 to 1 match, has been NHS traced and verified... do we need to issue a search?...can we just proceed to "main" form?
		userLookupController.startSearch("TEMPLATE");
		
	}
	

    /**
     * 
     * @param {*} callNumber 
     */
    function openCall(callNumber) {
        var serviceName = "CAS";
        var callServiceAlt = "";
        var cleoClientService = "";

        var launchOptions = {
            cxOneCallNo: callNumber // this triggers loadNewCall() when new case launched.
            }
        CallControllerClient.loadNewCallDocument(serviceName, callServiceAlt, cleoClientService, launchOptions);
    }

    /**
     * 
     * @param {*} callNumber 
     */
    function openCallFromGrid(callNumber) {
        openCall(callNumber);
    }

	/**
     * 
     * @return
     */
	return {
        processPayloadForNewCall: processPayloadForNewCall,
		loadNewCall: loadNewCall,
        openCallFromGrid: openCallFromGrid,
		mapDemographicsToCase: mapDemographicsToCase
	}
}