<template>
  <ag-grid-vue
    :style="gridStyle"
    class="ag-theme-balham"
    :columnDefs="columnDefs"
    :rowData="gridData"
    :grid-options="gridOptions"
    :modules="modules"
    :frameworkComponents="frameworkComponents"
    @grid-ready="onGridReady"
    @data-model-changed="dataModelChanged"
    @filter-changed="onFilterChanged"
    @processChanges="onProcessChanges"
  >
  </ag-grid-vue>
</template>

<script lang="ts">
import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-balham.css";
import { AgGridVue } from "ag-grid-vue";
import { GridOptions } from "@ag-grid-community/core";
import {
  GridApi,
  CellContextMenuEvent,
  RowClickedEvent,
  ValueGetterParams,
  CellClassParams,
  RowDataTransaction,
  RowNodeTransaction,
} from "@ag-grid-community/core";
import { RowDoubleClickedEvent } from "@ag-grid-community/core/dist/es6/events";
import { GetContextMenuItems } from "@ag-grid-community/core/dist/cjs/entities/gridOptions";
import { GetContextMenuItemsParams, ITooltipParams } from "ag-grid-community";
import CustomTooltip from "@/calls/summary/test-tooltip";

import { ClientSideRowModelModule } from "@ag-grid-community/client-side-row-model";

import Vue from "vue";
import Component from "vue-class-component";
import { Prop, Watch } from "vue-property-decorator";
import {
  CLEO_GRID_COLUMN_NAME,
  ICleoCallSummary,
  ISimpleTrigger,
} from "../summary/call-summarry-models";
import { CallSummaryService } from "@/calls/summary/call-summary-service";
import CallSummaryForm from "@/calls/summary/call-summary-form-tooltip.vue";
import CaseCommentsTooltip from "@/calls/grids/grids-named/ui/tooltip/CaseCommentsTooltip.vue";

import { loggerInstance } from "@/common/Logger";
import { debounce } from "@/common/debounce";
import { CleoRightClickAgGridService } from "@/calls/grids/ag-grid-specific/ag-grid-right-click";

import { CommonService } from "@/common/common-service";
import { GridFilterService } from "@/calls/grids/grid-filter/grid-filter-service";

import { CallSummaryHtml } from "@/calls/summary/call-summary-html";
import { CLEO_GRID_COLUMN_NAMES } from "../summary/call-summarry-models";
import {
  ColDef,
  GetQuickFilterTextParams,
} from "@ag-grid-community/core/dist/cjs/entities/colDef";
import { mapState } from "vuex";
import { IUserStoreState, USER_STORE_CONST } from "@/user/user-store";
import { CleoCommonService } from "@/common/cleo-common-service/cleo-common-service";
import { IGridFilter } from "@/calls/grids/grid-filter/grid-filter-models";
import { RowNode } from "@ag-grid-community/core/dist/cjs/entities/rowNode";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "@/common/config/config-store";
import { IGridDefinition } from "@/calls/grids/grid-models";
import { IAdapterAction } from "@/app-models";
import { formatUserDominoName, simpleObjectClone } from "@/common/common-utils";

const callSummaryHtml: CallSummaryHtml = new CallSummaryHtml();
const callSummaryService: CallSummaryService = new CallSummaryService();
const cleoRightClickAgGridService: CleoRightClickAgGridService = new CleoRightClickAgGridService();
const gridFilterService: GridFilterService = new GridFilterService();

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
//  @ts-ignore
@Component({
  name: "grid-default",
  components: { AgGridVue },
  computed: {
    ...mapState<IUserStoreState>(USER_STORE_CONST.USER__CONST_MODULE_NAME, {
      userStoreState: (state: IUserStoreState) => state,
    }),
    ...mapState<IConfigStoreState>(
      CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME,
      {
        configStoreState: (state: IConfigStoreState) => state,
        // legacyGridResize: (state: IConfigStoreState) => state.legacyGridResize,
        adapterCleoAction: (state: IConfigStoreState) =>
          state.adapterCleoAction,
      }
    ),
  },
})
export default class GridDefault extends Vue {
  public readonly userStoreState!: IUserStoreState;
  public readonly configStoreState!: IConfigStoreState;
  // public readonly legacyGridResize!: ISimpleTrigger<any>;
  public readonly adapterCleoAction!: IAdapterAction;

  @Prop({
    required: true,
  })
  public readonly gridDefinition!: IGridDefinition;

  @Prop({
    default: () => {
      return "width: 100%; height: 100%;";
    },
  })
  public readonly gridStyle!: string;

  @Prop({
    default: () => {
      return callSummaryService.getDefaultColumnNames();
    },
  })
  public readonly columnNames!: string[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly gridData!: ICleoCallSummary[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly newGridData!: ICleoCallSummary[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly updatedGridData!: ICleoCallSummary[];

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly removedGridData!: ICleoCallSummary[];

  @Prop({
    default: () => {
      return { timeIso: "" } as ISimpleTrigger<unknown>;
    },
  })
  public readonly recalcBreachTrigger!: ISimpleTrigger<unknown>;

  @Prop({
    default: false,
  })
  public readonly showMultiSelect!: boolean;

  @Prop({
    default: () => {
      return { timeIso: "" } as ISimpleTrigger<boolean>;
    },
  })
  public readonly selectAllTrigger!: ISimpleTrigger<boolean>;

  @Prop({
    default: () => {
      return { timeIso: "", data: "" } as ISimpleTrigger<string>;
    },
  })
  public readonly quickFilterTrigger!: ISimpleTrigger<string>;

  @Prop({
    default: () => {
      return { timeIso: "", data: "" } as ISimpleTrigger<string>;
    },
  })
  public readonly agFilters!: ISimpleTrigger<string>;

  @Prop({
    default: () => {
      return [];
    },
  })
  public readonly externalFilters!: IGridFilter[];

  @Prop({
    default: () => {
      return { timeIso: "", data: "" } as ISimpleTrigger<string>;
    },
  })
  public readonly exportGridDataTrigger!: ISimpleTrigger<string>;

  public resizeGridTrigger: ISimpleTrigger<unknown> = { timeIso: "" };

  public gridApi!: GridApi;
  public columnApi: any;
  public gridOptions: GridOptions = {};
  public iconColWidth = 20;
  public hoursMinsColWidth = 40;

  public debounceResize: any;

  public commonService: CommonService = new CommonService();
  public callSummaryService: CallSummaryService = new CallSummaryService();
  public callSummaryHtml: CallSummaryHtml = callSummaryHtml;
  public cleoCommonService: CleoCommonService = new CleoCommonService();

  public modules = [ClientSideRowModelModule];
  public CLEO_GRID_COLUMN_NAMES = CLEO_GRID_COLUMN_NAMES;

  public frameworkComponents: any;

  public columnDefs: ColDef[] = [];

  public timerResize!: number;
  public timerResizeLegacy!: number;
  public timerRightClick!: number;

  @Watch("newGridData")
  public onNewGridDataChanged(
    newValue: ICleoCallSummary[],
    oldValue: ICleoCallSummary[]
  ): void {
    loggerInstance.log("GridDefault.onNewGridDataChanged", {
      newValue,
      oldValue,
    });
    this.addCases(newValue);
  }

  @Watch("updatedGridData")
  public onUpdatedGridDataChanged(
    newValue: ICleoCallSummary[],
    oldValue: ICleoCallSummary[]
  ): void {
    loggerInstance.log("GridDefault.onUpdatedGridDataChanged", {
      newValue,
      oldValue,
    });
    this.updateCases(newValue);
  }

  @Watch("removedGridData")
  public onRemovedGridDataChanged(
    newValue: ICleoCallSummary[] | number[]
  ): void {
    loggerInstance.log("GridDefault.onRemovedGridDataChanged", {
      newValue,
    });
    this.removeCases(newValue);
  }
  //

  @Watch("externalFilters")
  public onExternalFiltersChanged(): void {
    //  @See doesExternalFilterPass
    this.gridOptions.api?.onFilterChanged();
  }

  @Watch("quickFilterTrigger")
  public onQuickFilterTextChanged(
    newValue: ISimpleTrigger<string>,
    oldValue: ISimpleTrigger<string>
  ): void {
    loggerInstance.log("GridDefault.onQuickFilterTextChanged", {
      newValue,
      oldValue,
    });
    loggerInstance.log(
      "GridDefault.onQuickFilterTextChange search: " + newValue.data
    );
    this.gridOptions.api?.setQuickFilter(newValue.data);
  }

  @Watch("exportGridDataTrigger")
  public onExportGridDataTriggerChanged(
    newValue: ISimpleTrigger<string>,
    oldValue: ISimpleTrigger<string>
  ): void {
    loggerInstance.log("GridDefault.exportGridDataTrigger", {
      newValue,
      oldValue,
    });
    loggerInstance.log(
      "GridDefault.exportGridDataTrigger search: " + newValue.data
    );
    let rowData: ICleoCallSummary[] = [];
    this.gridOptions.api?.forEachNode((node) => rowData.push(node.data));
    this.$emit("exportGridData", rowData);
  }

  @Watch("gridData")
  public onGridDataChanged(
    newValue: ISimpleTrigger<string>,
    oldValue: ISimpleTrigger<string>
  ): void {
    // loggerInstance.log("GridDefault.onGridDataChanged", {
    //   newValue,
    //   oldValue
    // });
    if (this.gridApi) {
      this.gridApi.sizeColumnsToFit();

      const numberOfRows = this.gridApi.getModel().getRowCount();
      // console.log("n n n n n n n " + numberOfRows);

      //  if no rows of data, show the no rows overlay.
      if (numberOfRows === 0) {
        // console.log(
        //   "-----------------GridDefault.onGridDataChanged no rows of data"
        // );
        this.gridApi.showNoRowsOverlay();
      } else {
        // console.log(
        //   "++++++++++++++++++GridDefault.onGridDataChanged has rows of data"
        // );
        this.gridApi.hideOverlay();
      }
    }
  }

  /*
  @Watch("legacyGridResize")
  public onLegacyGridResizeChanged(
    newValue: ISimpleTrigger<string>,
    oldValue: ISimpleTrigger<string>
  ): void {
    console.log("R R R R R R R R R GridDefault.legacyGridResize", {
      newValue,
      oldValue
    });
    loggerInstance.log("GridDefault.legacyGridResize", {
      newValue,
      oldValue
    });
    if (this.gridApi) {
      this.timerResizeLegacy = window.setTimeout(() => {
        this.gridApi.sizeColumnsToFit();
        window.clearTimeout(this.timerResizeLegacy);
      }, 1000);
    }
  }
  */

  // watch configStoreState
  // @Watch("configStoreState")
  // public onConfigStoreStateChanged(
  //   newValue: IConfigStoreState,
  //   oldValue: IConfigStoreState
  // ): void {
  //   console.log("C C C C C C C C C GridDefault.configStoreState", {
  //     newValue,
  //     oldValue
  //   });
  //   loggerInstance.log("GridDefault.configStoreState", {
  //     newValue,
  //     oldValue
  //   });
  // }

  public created(): void {
    loggerInstance.log("GridDefault.created()");

    const gridColumns: Record<CLEO_GRID_COLUMN_NAME, ColDef> = this
      .agGridColumnDefinitions;
    const availableColumnNames: CLEO_GRID_COLUMN_NAME[] = Object.keys(
      gridColumns
    ) as CLEO_GRID_COLUMN_NAME[];

    // Add any custom columns from grid definition
    if (this.gridDefinition.colDefintions.customColumns) {
      Object.entries(this.gridDefinition.colDefintions.customColumns).forEach(
        ([key, colDef]) => {
          gridColumns[key as CLEO_GRID_COLUMN_NAME] = colDef;
          if (!availableColumnNames.includes(key as CLEO_GRID_COLUMN_NAME)) {
            availableColumnNames.push(key as CLEO_GRID_COLUMN_NAME);
          }
        }
      );
    }

    const colDefsToLoad: ColDef[] = [];

    // Otherwise use the existing include/exclude logic
    const hasOnlyIncludeFields =
      this.gridDefinition.colDefintions.onlyIncludeTheseFields.length > 0;
    const hasExcludeFields =
      this.gridDefinition.colDefintions.excludeTheseFields.length > 0;

    console.warn(
      "hasOnlyIncludeFields: " + hasOnlyIncludeFields,
      this.gridDefinition.colDefintions.onlyIncludeTheseFields
    );
    console.warn(
      "hasExcludeFields: " + hasExcludeFields,
      this.gridDefinition.colDefintions.excludeTheseFields
    );

    if (hasOnlyIncludeFields && hasExcludeFields) {
      throw new Error(
        "GridDefault.created() - can not have both onlyIncludeFields and excludeFields"
      );
    }

    if (hasOnlyIncludeFields) {
      this.gridDefinition.colDefintions.onlyIncludeTheseFields.forEach(
        (columnName) => {
          if (gridColumns[columnName]) {
            const colDef = gridColumns[columnName];

            // since the col might be one we keep hidden most of the time.
            colDef.hide = false;
            colDefsToLoad.push(colDef);
          }
        }
      );
    } else {
      // If explicit column order is provided, use it
      if (
        this.gridDefinition.colDefintions.columnOrder &&
        this.gridDefinition.colDefintions.columnOrder.length > 0
      ) {
        this.gridDefinition.colDefintions.columnOrder.forEach((columnName) => {
          if (gridColumns[columnName]) {
            if (
              this.gridDefinition.colDefintions.excludeTheseFields.indexOf(
                columnName
              ) === -1
            ) {
              colDefsToLoad.push(gridColumns[columnName]);
            }

            // colDefsToLoad.push(gridColumns[columnName]);
          }
        });
      } else {
        if (!hasOnlyIncludeFields && !hasExcludeFields) {
          //  load all columns
          availableColumnNames.forEach((columnName: CLEO_GRID_COLUMN_NAME) => {
            colDefsToLoad.push(gridColumns[columnName]);
          });
        } else {
          if (hasExcludeFields) {
            availableColumnNames.forEach(
              (columnName: CLEO_GRID_COLUMN_NAME) => {
                // only push the column if it is not in the exclude list
                if (
                  this.gridDefinition.colDefintions.excludeTheseFields.indexOf(
                    columnName
                  ) === -1
                ) {
                  colDefsToLoad.push(gridColumns[columnName]);
                }
              }
            );
          }
        }
      }
    }

    // TODO  this should be on gridDefinition
    if (this.showMultiSelect) {
      colDefsToLoad.push({
        headerName: "",
        checkboxSelection: true,
        width: this.iconColWidth,
        field: "CallNumber",
        headerCheckboxSelection: function() {
          return false;
        },
      });
    }

    this.columnDefs = colDefsToLoad;

    this.frameworkComponents = {
      customTooltip: CallSummaryForm,
      caseCommentsTooltip: CaseCommentsTooltip,
    };

    this.gridOptions = {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //  @ts-ignore
      columnDefs: this.columnDefs,

      defaultColDef: {
        suppressMenu: true,
        suppressMovable: true,
        filter: false,
        filterParams: {
          filterOptions: ["contains", "startsWith", "endsWith"],
          defaultOption: "startsWith",
        },
        resizable: false,
      },
      rowData: this.gridData,
      // floatingFilter: true,  //  shows native ag-grid filter.
      pagination: false,
      paginationPageSize: 2000,
      enableCellChangeFlash: true,
      suppressChangeDetection: false,
      colResizeDefault: "yes",
      rowSelection: this.showMultiSelect ? "multiple" : "single", //  "multiplex",
      // rowMultiSelectWithClick: true,
      immutableData: true,
      animateRows: true,
      overlayNoRowsTemplate: "<span>No rows to display.</span>",
      // suppressContextMenu: true,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //  @ts-ignore
      getContextMenuItems: this.getContextMenuItems,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //  @ts-ignore
      popupParent: document.querySelector("grid--content"),
      tooltipShowDelay: 0,
      // tooltipHideDelay: 5000,  // doesn't work in this version.
      frameworkComponents: {
        customTooltip: CustomTooltip,
        caseCommentsTooltip: CaseCommentsTooltip,
      },
      isExternalFilterPresent: function() {
        return true; // if true, overrides native ag-grid filter.
      },
      doesExternalFilterPass: (node: RowNode): boolean => {
        const cleoCallSummary: ICleoCallSummary = node.data;
        return gridFilterService.applyFiltersToCall(
          cleoCallSummary,
          this.externalFilters
        );
      },
      onFilterChanged: (eventFilter: any) => {
        this.$emit(
          "onFilterChangedRowCount",
          this.gridApi.getDisplayedRowCount()
        );
      },
      getRowNodeId: (cleoCallSummary: ICleoCallSummary) => {
        return cleoCallSummary.CallNo.toString();
      },
      onRowDoubleClicked: (gridEvent: any) => {
        const grEvent: RowDoubleClickedEvent = gridEvent;

        console.warn("!!!! ! ! ! ! !onRowDoubleClicked", grEvent.data);

        const cleoCallSummary: ICleoCallSummary = (grEvent.data as any) as ICleoCallSummary;
        if (cleoCallSummary.CallNo === 0) {
          return;
        }

        this.$emit("onRowDoubleClicked", { ...grEvent.data });
      },
      onCellContextMenu: (cellEvent: any) => {
        const cellContextMenuEvent: CellContextMenuEvent = cellEvent;
        const cleoCallSummary: ICleoCallSummary = {
          ...cellContextMenuEvent.data,
        };
        loggerInstance.log(
          "GridDefault.onCellContextMenu() CallNo: " + cleoCallSummary.CallNo
        );
        if (cleoCallSummary.CallNo === 0) {
          return;
        }
        cellContextMenuEvent.node.setSelected(true, true);
        const triggerEvent: MouseEvent = (cellContextMenuEvent.event as any) as MouseEvent;
        if (triggerEvent) {
          this.$emit("onCellContextMenu", {
            data: cleoCallSummary,
            coords: {
              x: triggerEvent.clientX,
              y: triggerEvent.clientY,
            },
          });
        }
      },
      onRowClicked: (rowEvent: any) => {
        const rEvent: RowClickedEvent = rowEvent;
        const cleoCallSummary: ICleoCallSummary = { ...rEvent.data };

        if (cleoCallSummary.CallNo === 0) {
          return;
        }

        loggerInstance.log(
          "GridDefault.onRowClicked() CallNo: " + cleoCallSummary.CallNo
        );
        this.$emit("onRowClicked", cleoCallSummary);
        this.$emit("onCurrentSelectedDocs", rEvent.api.getSelectedRows());
      },
      getRowStyle: (params: CellClassParams) => {
        const cleoCallSummary: ICleoCallSummary = params.data;

        const dxPriorityCodes = this.getBreachPriorityDxCodes();
        const warnUrgentMinutes = this.configStoreState.failedContactWarnMinutes
          .urgent;
        const warnNotUrgentMinutes = this.configStoreState
          .failedContactWarnMinutes.notUrgent;

        return this.callSummaryHtml.getRowStyle(
          cleoCallSummary,
          dxPriorityCodes,
          warnUrgentMinutes,
          warnNotUrgentMinutes,
          new Date()
        ).style;
      },
      // components: {
      //   //  @ts-ignore
      //   customTooltip: CustomTooltip
      // }
    };

    this.debounceResize = debounce(() => {
      loggerInstance.log("GridDefault.debounceResize()");
      this.gridOptions.api?.sizeColumnsToFit();
    }, 250);

    window.addEventListener("resize", this.debounceResize);
  }

  public onGridReady(params: any): void {
    loggerInstance.log("AgGridCleo.onGridReady():", params);
    this.gridApi = params.api;
    this.columnApi = params.columnApi;

    //  TODO data retrieved takes x ms to arrive, it doesn't resize
    //  TODO correctly...
    this.gridApi.sizeColumnsToFit();

    this.timerResize = window.setTimeout(() => {
      //  TODO ...hence this...
      this.gridApi.sizeColumnsToFit();

      // this.columnApi.autoSizeColumn("CallClassification");

      window.clearTimeout(this.timerResize);
    }, 1000);
  }

  public dataModelChanged(rowData: ICleoCallSummary[]): void {
    // loggerInstance.log(
    //   "AgGridCleo.dataModelChanged() rowData.length:" + rowData.length,
    //   { ...rowData }
    // );
    //  push this back to store.
  }

  public onFilterChanged(x: any, y: any): void {
    // loggerInstance.error(
    //   "F F F F F F F F F  GridDefault.onFilterChanged()",
    //   x,
    //   y
    // );
  }

  public onProcessChanges(resp: any): void {
    // loggerInstance.log("C C C C C C C GridDefault.onProcessChanges():", resp);
  }

  public getBreachPriorityDxCodes(): string[] {
    return this.configStoreState.priorityDxCodes;
  }

  public reachedFailedContactWarn(cleoCallSummary: ICleoCallSummary): boolean {
    const warnUrgentMinutes = this.configStoreState.failedContactWarnMinutes
      .urgent;
    const warnNotUrgentMinutes = this.configStoreState.failedContactWarnMinutes
      .notUrgent;

    return this.callSummaryHtml.reachedFailedContactWarn(
      cleoCallSummary,
      warnUrgentMinutes,
      warnNotUrgentMinutes,
      new Date()
    );
  }

  public onRowUpdated(resp: any): void {
    loggerInstance.log("GridDefault.onRowUpdated():", resp);
  }

  public agGridColumnDefinitions: Record<CLEO_GRID_COLUMN_NAME, ColDef> = {
    FURTHER_ACTION: {
      headerName: "Further Action",
      field: "CallFAction",
      width: 120,
      hide: true,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallFAction || "";
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallFAction || "";
      },
    },
    FURTHER_ACTION_GP: {
      headerName: "GP Action",
      field: "Cpl_furtherActionGPText",
      width: 120,
      hide: true,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.Cpl_furtherActionGPText || "";
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.Cpl_furtherActionGPText || "";
      },
    },
    NHS_NO: {
      headerName: "NHS No",
      field: "CallNhsNo",
      width: 50,
      hide: true,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallNhsNo || "";
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallNhsNo || "";
      },
    },
    CALL_PRACTICE: {
      headerName: "Call Practice",
      field: "CallPracticeOCS",
      width: 80,
      hide: true,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallPractice || "";
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallPractice || "";
      },
    },
    LOCKED: {
      headerName: "",
      field: "IsLocked",
      headerTooltip: "Is call locked.",
      width: this.iconColWidth,
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.isLocked(cleoCallSummary);
      },
    },
    CALLBACK: {
      headerName: "",
      field: "CallCallback",
      headerTooltip: "How many times a person has called in.",
      width: this.iconColWidth,
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.callCallBack(cleoCallSummary);
      },
    },
    CAS_VALIDATION: {
      headerName: "",
      field: "CasValidationCount",
      width: this.iconColWidth,
      headerTooltip: "Has case been validated?",
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.casValidation(cleoCallSummary);
      },
    },
    WALK_IN: {
      headerName: "",
      field: "DispatchVehicle",
      headerTooltip: "Is call walk in.",
      width: this.iconColWidth,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.WalkIn + cleoCallSummary.DispatchVehicle;
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.walkIn(cleoCallSummary);
      },
    },
    WARM_TRANSFERRED: {
      headerName: "",
      field: "CallWarmTransferred",
      headerTooltip: "Has call been warm transferred.",
      width: this.iconColWidth,
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.callWarmTransferred(cleoCallSummary);
      },
    },
    BREACH_LEVEL_HUMAN: {
      headerName: "",
      field: "BreachPriority",
      width: 40,
      minWidth: 40,
      headerTooltip: "Breach priority.",
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.breachLevelHuman(cleoCallSummary);
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.breachLevelHuman(cleoCallSummary);
      },
    },
    DX: ({
      headerName: "DX",
      width: 50,
      minWidth: 50,
      headerTooltip: "DX code.",
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryService.getDxCode(cleoCallSummary);
      },
      tooltipValueGetter: (params: ValueGetterParams) => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryService.getDxCodeTooltip(cleoCallSummary);
      },
    } as unknown) as ColDef,
    BREACH: {
      headerName: "Breach",
      field: "BreachActualTime",
      width: 60,
      minWidth: 60,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.BreachActualTime
          ? cleoCallSummary.BreachActualTime
          : "";
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }

        const dxPriority = this.getBreachPriorityDxCodes();
        let warnUrgentMinutes = 20;
        let warnNotUrgentMinutes = 40;
        if (this.configStoreState) {
          warnUrgentMinutes = this.configStoreState.failedContactWarnMinutes
            .urgent;
          warnNotUrgentMinutes = this.configStoreState.failedContactWarnMinutes
            .notUrgent;
        }

        return this.callSummaryHtml.breachActualTime(
          cleoCallSummary,
          dxPriority,
          warnUrgentMinutes,
          warnNotUrgentMinutes,
          new Date()
        );
      },
    },
    FOLLOW_UP_Active: {
      headerName: "Active",
      field: "FOLLOW_UP_Active",
      width: 80,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.getFollowUpActiveTime(cleoCallSummary);
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.FOLLOW_UP_Active
          ? cleoCallSummary.FOLLOW_UP_Active
          : "";
      },
    },
    CALL_NO: {
      headerName: "CallNo",
      field: "CallNo",
      width: 70,
      minWidth: 70,
      filter: "agNumberColumnFilter",
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallNo.toString();
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        return cleoCallSummary.CallNo.toString();
      },
    },
    PDS: {
      headerName: "",
      field: "PDSTracedAndVerified",
      headerTooltip:
        "Has patient been traced and verified, green: yes, red: no.",
      width: this.iconColWidth,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.PdsTracedAndVerified.toString();
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.pdsTracedAndVerified(cleoCallSummary);
      },
    },
    SERVICE: {
      headerName: "Service",
      field: "CallService",
      width: 130,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryService.getService(cleoCallSummary);
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryService.getService(cleoCallSummary);
      },
    },
    PATIENT: {
      headerName: "Patient",
      field: "PatientName",
      width: 150,
      cellClass: "grid-cell-centered",
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryService.getPatientName(cleoCallSummary);
      },
      getQuickFilterText: function(params: GetQuickFilterTextParams): string {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return callSummaryService.getPatientName(cleoCallSummary);
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.patientName(cleoCallSummary);
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryService.getPatientName(cleoCallSummary);
      },
    },
    PATIENT_AGE: {
      headerName: "Age",
      field: "CallAge",
      width: 40,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallAge + " " + cleoCallSummary.CallAgeClass;
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallAge + " " + cleoCallSummary.CallAgeClass;
      },
    },
    DOB: {
      headerName: "DOB",
      field: "CallDobIso",
      width: 50,
      hide: true,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        return this.callSummaryService.getDOB(cleoCallSummary);
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        return this.callSummaryService.getDOB(cleoCallSummary);
      },
    },
    PLS_ACTION: {
      headerName: "PLS Action",
      field: "PLS_ACTIONTEXT",
      width: 150,
      headerTooltip: "PLS Action",
      hide: true,
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.PLS_ACTIONTEXT;
      },
    },
    PLS: {
      headerName: "PLS Comment",
      field: "PLS_REASON",
      headerTooltip: "PLS",
      width: 80,
      hide: true,
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.PLS_REASON;
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.PLS_REASON;
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return (
          cleoCallSummary.PLS_TIME +
          " " +
          formatUserDominoName(cleoCallSummary.PLS_USER, "CN") +
          ": " +
          cleoCallSummary.PLS_REASON
        );
      },
    },
    TOWN: {
      headerName: "Town",
      field: "CallTown",
      width: 80,
      getQuickFilterText: function(params: GetQuickFilterTextParams): string {
        return params.value;
      },
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryService.getTown(cleoCallSummary);
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryService.getTown(cleoCallSummary);
      },
    },
    RELATIONSHIP: {
      headerName: "",
      field: "CallCRel",
      width: this.iconColWidth,
      headerTooltip: "Relationship to patient",
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        const relationship = cleoCallSummary.CallCRel.toUpperCase();
        const subClassification =
          cleoCallSummary.CallSubClassification &&
          cleoCallSummary.CallSubClassification.Description
            ? cleoCallSummary.CallSubClassification.Description.toUpperCase()
            : "";
        return relationship + subClassification;
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.callCRel(cleoCallSummary);
      },
    },
    TEL_ALT: {
      headerName: "",
      field: "CallTelNoAlt1",
      width: this.iconColWidth,
      headerTooltip: "Alternative telephone number",
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return (
          cleoCallSummary.CallTelNoAlt1 + cleoCallSummary.CallTelNoAltType1
        );
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.callAltTel(cleoCallSummary);
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.callAltTel(cleoCallSummary);
      },
    },
    CALL_HCP: {
      headerName: "",
      field: "Call_HCP",
      width: this.iconColWidth,
      headerTooltip: "Has call been assigned to a HCP",
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.callHcp(cleoCallSummary);
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.CallCRel;
      },
    },
    FIRST_CONTACT: {
      headerName: "",
      field: "Call1stContact",
      width: this.iconColWidth,
      headerTooltip: "First contact",
      valueGetter: (params: ValueGetterParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return (
          (cleoCallSummary.Call1StContact
            ? cleoCallSummary.Call1StContact
            : "") +
          cleoCallSummary.Call1StContactPathways +
          cleoCallSummary.PatientContactCode +
          cleoCallSummary.PatientContactCodeCount
        );
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.call1stContact(cleoCallSummary);
      },
    },
    GOODSAM_IMAGE_STATUS: {
      headerName: "",
      field: "GOODSAM_IMAGE_STATUS",
      width: this.iconColWidth,
      headerTooltip: "Photos Requested/Received",
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.media(cleoCallSummary);
      },
    },
    ASSIGNED_TO: {
      headerName: "Assigned To",
      field: "CallDoctorName",
      width: 100,
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        // return cleoCallSummary.CallDoctorName;

        return this.cleoCommonService.formatUserDominoName(
          cleoCallSummary.CallDoctorName,
          "CN"
        );
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.cleoCommonService.formatUserDominoName(
          cleoCallSummary.CallDoctorName,
          "CN"
        );
      },
    },
    CASE_COMMENTS: {
      headerName: "",
      field: "CaseComments",
      headerTooltip: "Case Comments",
      width: this.iconColWidth,
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.getCaseComments(cleoCallSummary);
      },
      // tooltipValueGetter: (params: ITooltipParams): string => {
      //   const cleoCallSummary: ICleoCallSummary = params.data;
      //   return cleoCallSummary.CaseComments &&
      //     cleoCallSummary.CaseComments.length
      //     ? cleoCallSummary.CallNo
      //     : null;
      // },
      tooltipComponent: "caseCommentsTooltip",
      tooltipField: "CallNo", //  This is required, but I don't understand it.
    },
    RECEIVED: {
      headerName: "Rec",
      field: "CallReceivedTime",
      headerTooltip: "Call Received Time",
      width: this.hoursMinsColWidth,
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.getGenericDateTime(
          cleoCallSummary.CallReceivedTime
        );
      },
    },
    APPOINTMENT_TIME: {
      headerName: "App",
      field: "CallAppointmentTime",
      headerTooltip: "Appointment Time",
      width: this.hoursMinsColWidth,
      cellClass: "grid-cell-centered",
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.callAppointmentTime(cleoCallSummary);
      },
    },
    ARRIVED: {
      headerName: "Arr",
      width: this.hoursMinsColWidth,
      field: "CallArrivedTime",
      headerTooltip: "Patient Arrived Time",
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        if (cleoCallSummary.CallArrivedTime) {
          return this.callSummaryHtml.getGenericDateTime(
            cleoCallSummary.CallArrivedTime
          );
        }
        return "";
      },
    },
    CLASSIFICATION: {
      headerName: "Classification",
      field: "CallClassification",
      filter: "agTextColumnFilter",
      valueGetter: function(params: ValueGetterParams): string {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return callSummaryService.getClassification(cleoCallSummary);
      },
      getQuickFilterText: function(params: GetQuickFilterTextParams): string {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return callSummaryService.getClassification(cleoCallSummary);
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return callSummaryService.getClassification(cleoCallSummary);
      },
      tooltipValueGetter: (params: ITooltipParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        //return callSummaryService.getClassification(cleoCallSummary);
        return (
          "Class: " +
          cleoCallSummary.CallClassification.Description +
          ", SubClass: " +
          cleoCallSummary.CallSubClassification.Description +
          ", Client Service: " +
          cleoCallSummary.cleoClientService +
          ", Triage: " +
          cleoCallSummary.OVERSIGHT_BASE_TRIAGE_TYPE +
          ", Support: " +
          cleoCallSummary.Cpl_supportTypeRequired
        );
      },
    },
    SMS: {
      headerName: "",
      width: this.iconColWidth,
      field: "SMS_LATEST_AT",
      headerTooltip: "Has an SMS been sent.",
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.smsSent(cleoCallSummary);
      },
    },
    COMFORT_CALL: {
      headerName: "",
      width: this.iconColWidth,
      field: "CourtesyCount",
      headerTooltip: "Comfort Call",
      valueGetter: function(params: ValueGetterParams): string {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        const comfortSentServiceTime = cleoCallSummary.ComfortSentServiceTime
          ? cleoCallSummary.ComfortSentServiceTime
          : "";
        const comfortSmsTime = cleoCallSummary.ComfortSmsTime
          ? cleoCallSummary.ComfortSmsTime
          : "";

        const comfortSentServiceTime2 = cleoCallSummary.ComfortSentService2Time
          ? cleoCallSummary.ComfortSentService2Time
          : "";
        const comfortSmsTime2 = cleoCallSummary.ComfortSmsTime2
          ? cleoCallSummary.ComfortSmsTime2
          : "";

        //  This is a manual process.
        const courtesyTime = cleoCallSummary.CourtesyTime
          ? cleoCallSummary.CourtesyTime
          : "";

        return (
          comfortSentServiceTime +
          comfortSmsTime +
          courtesyTime +
          comfortSentServiceTime2 +
          comfortSmsTime2
        );
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.comfortCall(cleoCallSummary);
      },
    },
    MORE_INFO: {
      headerName: "",
      headerTooltip: "More Info",
      width: this.iconColWidth,
      valueGetter: function(params: ValueGetterParams): string {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return cleoCallSummary.OversightValidationType;
      },
      cellRenderer: (params: CellClassParams): string => {
        const cleoCallSummary: ICleoCallSummary = params.data;
        if (cleoCallSummary.CallNo === 0) {
          return "";
        }
        return this.callSummaryHtml.moreInfo(cleoCallSummary);
      },
      tooltipComponent: "customTooltip",
      tooltipField: "CallNo", //  This is required, but I don't understand it.
    },
  };

  public getContextMenuItems(
    params: GetContextMenuItemsParams
  ): GetContextMenuItems {
    const cleoCallSummary: ICleoCallSummary = this.commonService.simpleObjectClone(
      params.node.data
    ) as ICleoCallSummary;
    const callNo = cleoCallSummary.CallNo;
    const cleoCallSummaries: ICleoCallSummary[] = this.showMultiSelect
      ? (params.api?.getSelectedRows() as ICleoCallSummary[])
      : [];

    const menuItems = cleoRightClickAgGridService.getContextMenuItems(
      cleoCallSummary,
      cleoCallSummaries,
      this.userStoreState.user,
      this.showMultiSelect
    );

    //  Give right click time to render
    this.timerRightClick = window.setTimeout(() => {
      cleoRightClickAgGridService.disableMenuItems(
        callNo,
        this.userStoreState.user.userRole
      );
    }, 100);

    return menuItems;
  }

  public addCases(cleoCallSummaries: ICleoCallSummary[]): void {
    const callsNew = cleoCallSummaries.filter((cleoCallSummary) => {
      return cleoCallSummary.CallStatusValue === 1;
    });

    // const rowDataTransaction: RowDataTransaction = {
    //   add: callsNew
    // };
    // this.gridApi.applyTransactionAsync(rowDataTransaction, this.updateResult);
    // this.updateCases(callsNew);
    this.insertCasesWithCorrectIndex(callsNew);
  }

  public updateCases(cleoCallSummaries: ICleoCallSummary[]): void {
    if (cleoCallSummaries.length === 0) {
      return;
    }

    const transactionData: RowDataTransaction = cleoCallSummaries.reduce<
      RowDataTransaction
    >(
      (accum, cleoCall) => {
        if (cleoCall.CallStatusValue === 1) {
          //  Get sent an "update" socket message, but can't find it in grid...BUT...
          //  messages are specific to grids, so:
          //  A.  Why did we not get a "New" message...maybe errored going in to Adapter.
          //  B.  This allows us to get it in grid, even if failed.
          const existsInGrid = this.gridApi.getRowNode(
            cleoCall.CallNo.toString()
          );
          if (existsInGrid) {
            accum.update?.push(cleoCall);
          } else {
            accum.add?.push(cleoCall);
          }
        } else {
          //  status not 1, we don't want it.
          accum.remove?.push(cleoCall);
        }
        return accum;
      },
      {
        add: [],
        update: [],
        remove: [],
      }
    );

    //  Calls that already exist in grid...just update them...or remove
    if (
      transactionData.update!.length > 0 ||
      transactionData.remove!.length > 0
    ) {
      this.gridApi.applyTransactionAsync(
        {
          update: transactionData.update,
          remove: transactionData.remove!,
        },
        this.gridUpdateResult
      );
    }

    if (transactionData.add && transactionData.add.length > 0) {
      this.insertCasesWithCorrectIndex(transactionData.add);
    }
  }

  public insertCasesWithCorrectIndex(
    cleoCallSummaries: ICleoCallSummary[]
  ): void {
    const callsNew = cleoCallSummaries.filter((cleoCallSummary) => {
      return cleoCallSummary.CallStatusValue === 1;
    });

    const addCasesBreach: {
      withBreach: ICleoCallSummary[];
      withoutBreach: ICleoCallSummary[];
    } = {
      withBreach: [],
      withoutBreach: [],
    };
    callsNew.forEach((cleoCallSummary) => {
      if (
        cleoCallSummary.BreachActualTime &&
        cleoCallSummary.BreachActualTime.length > 0
      ) {
        addCasesBreach.withBreach.push(cleoCallSummary);
      } else {
        addCasesBreach.withoutBreach.push(cleoCallSummary);
      }
    });

    //  Without breach...just add them, they don't need to go into "correct" sorted order.
    if (addCasesBreach.withoutBreach.length > 0) {
      this.gridApi.applyTransactionAsync(
        {
          add: addCasesBreach.withoutBreach,
        },
        this.gridUpdateResult
      );
    }

    //  TODO this feels not right...if the grid gets big, there are going to
    //  TODO potentially be lots of iterations.

    if (addCasesBreach.withBreach.length > 0) {
      /*
      let indexToUse = 0;

      const cleoCall = addCasesBreach.withBreach[0];
      const breachTimeOfCallToAdd = cleoCall.BreachActualTime!;

      const callsInGrid: ICleoCallSummary[] = [];
      this.gridApi.forEachNode(rowNode => {
        callsInGrid.push(rowNode.data);
      });

      for (let i = 0; i < callsInGrid.length; i++) {
        const cleoCallRow = callsInGrid[i];
        const breachTimeOfCallRow = cleoCallRow.BreachActualTime!;
        const applyBreach = callSummaryService.applyBreachToCall(cleoCallRow);
        indexToUse = i;
        if (breachTimeOfCallToAdd < breachTimeOfCallRow || !applyBreach) {
          break;
        }
      }

      this.gridApi.applyTransactionAsync(
        {
          add: [cleoCall],
          addIndex: indexToUse
        },
        this.gridUpdateResult
      );
      */

      addCasesBreach.withBreach.forEach((cleoCall) => {
        let indexToUse = 0;
        const breachTimeOfCallToAdd = cleoCall.BreachActualTime!;

        const callsInGrid: ICleoCallSummary[] = [];
        this.gridApi.forEachNode((rowNode) => {
          callsInGrid.push(rowNode.data);
        });

        for (let i = 0; i < callsInGrid.length; i++) {
          const cleoCallRow = callsInGrid[i];
          const breachTimeOfCallRow = cleoCallRow.BreachActualTime!;
          const applyBreach = callSummaryService.applyBreachToCall(cleoCallRow);
          indexToUse = i;
          if (breachTimeOfCallToAdd < breachTimeOfCallRow || !applyBreach) {
            break;
          }
        }

        this.gridApi.applyTransactionAsync(
          {
            add: [cleoCall],
            addIndex: indexToUse,
          },
          this.gridUpdateResult
        );
      });
    }
  }

  public removeCases(cleoCallObjs: ICleoCallSummary[] | number[]): void {
    if (cleoCallObjs.length === 0) {
      return;
    }
    let callsToRemove: Partial<ICleoCallSummary>[] = [];
    if (typeof cleoCallObjs[0] === "number") {
      callsToRemove = (cleoCallObjs as number[]).map((callNo: number) => {
        return {
          CallNo: callNo,
        };
      });
    } else {
      callsToRemove = (cleoCallObjs as ICleoCallSummary[]).map(
        (cleoCallSummary: ICleoCallSummary) => {
          return {
            CallNo: cleoCallSummary.CallNo,
          };
        }
      );
    }

    this.gridApi.applyTransactionAsync(
      {
        remove: callsToRemove,
      },
      this.gridUpdateResult
    );
  }

  public gridUpdateResult(res: RowNodeTransaction): void {
    loggerInstance.log("GridDefault.updateResult", res);
    //  After socket message, if col being filled in
    // if (this.gridApi) {
    //   this.gridApi.sizeColumnsToFit();
    // }
    this.$emit("currentGridRowCount", this.gridApi.getModel().getRowCount());
  }

  public findCleoCallFromSocketResponse(
    cleoCallSummary: ICleoCallSummary,
    cleoCallSummaries: ICleoCallSummary[]
  ): ICleoCallSummary | null {
    const cleoCalls = cleoCallSummaries.filter((callSummary) => {
      return (
        cleoCallSummary.CallNo.toString() === callSummary.CallNo.toString()
      );
    });
    if (cleoCalls.length === 1) {
      return cleoCalls[0];
    }
    return null;
  }

  @Watch("recalcBreachTrigger")
  public onRecalcBreachTriggerChanged(
    newValue: ISimpleTrigger<unknown>,
    oldValue: ISimpleTrigger<unknown>
  ): void {
    if (newValue.timeIso !== oldValue.timeIso) {
      this.recalcBreach();
    }
  }

  public recalcBreach(): void {
    const params = {
      force: true,
      suppressFlash: true,
      columns: ["BreachActualTime"],
    };
    this.gridApi.refreshCells(params);
  }

  @Watch("selectAllTrigger")
  public onSelectAllTriggerChanged(
    newValue: ISimpleTrigger<boolean>,
    oldValue: ISimpleTrigger<boolean>
  ): void {
    if (newValue.timeIso !== oldValue.timeIso) {
      if (typeof newValue.data !== "boolean") {
        return;
      }
      this.selectAll(newValue.data);
    }
  }

  public selectAll(selectAll: boolean): void {
    if (!this.gridOptions.api) {
      return;
    }
    selectAll
      ? this.gridOptions.api?.selectAll()
      : this.gridOptions.api?.deselectAll();
    this.$emit("onCurrentSelectedDocs", this.gridOptions.api.getSelectedRows());
  }

  public beforeDestroy(): void {
    loggerInstance.log(
      "GridDefault.beforeDestroy(); remove this.debounceResize"
    );
    window.removeEventListener("resize", this.debounceResize);
    window.clearTimeout(this.timerResize);
    window.clearTimeout(this.timerRightClick);
  }

  public destroyed(): void {
    loggerInstance.log("GridDefault.destroyed();");
  }

  public getCurrentColumnOrder(): CLEO_GRID_COLUMN_NAME[] {
    return Object.keys(this.agGridColumnDefinitions) as CLEO_GRID_COLUMN_NAME[];
  }
}
</script>

<style>
.ag-cell {
  padding-left: 2px !important;
  padding-right: 2px !important;
}

/* ROW SELECTION */
.ag-row-selected {
  /*background-color: rgba(220, 53, 69, 1) !important; !* red *!*/
  background-color: #aec7e3 !important;
  color: black !important;
}

/* Have also put this in cleo_v2012.11.27.css in legacy app to prevent another
release.  Issue is in some weird scenario I can't repro, the horz scroll bar overalps
the last row, known edge case bug in ag-grid v24.  This style just leaves a bit of space
which should alleviate issue.  ALL CHANGE...still errors on some devices
Using a dummy empty row.*/
/* .ag-body-viewport {
  padding-bottom: 40px !important;
} */

.tooltip {
  position: relative;
  display: inline-block;
  border-bottom: 1px dotted black;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  height: 150px;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;

  /* Position the tooltip */
  position: fixed;
  z-index: 999;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
}

.call-summary-row--selected {
  background-color: deeppink !important;
}

.call-summary-row-priority {
  background-color: red !important;
}

.my-green-class {
  background-color: green !important;
}

.my-yellow-class {
  background-color: yellow !important;
}

.level-1-breach-priority--standard {
  background-color: #ff8c00 !important;
  color: black !important;
}

.level-1-breach-priority--warn {
  background-color: red !important;
  color: white !important;
}

.level-1-breach-priority--breachPre {
  background-color: black !important;
  color: white !important;
}

.level-1-breach-priority--breach {
  background-color: black !important;
  color: white !important;
}

.row-clini-high-priority {
  background-color: #ffe5ff !important;
}

.failed-contact--standard {
  color: #b0b0b0 !important;
}

.failed-contact--warn {
  background-color: #fff2e5 !important;
}
</style>
